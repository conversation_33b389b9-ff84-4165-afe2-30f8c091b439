"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { supabase } from "@/lib/supabase";
import { Plus, Edit2, Trash2, Building2, MessageCircle } from "lucide-react";

interface UMKM {
  id: string;
  name: string;
  description: string;
  category: string;
  owner_name: string;
  contact_phone: string;
  contact_email: string | null;
  address: string;
  image_url: string | null;
}

export function UMKMManager() {
  const [umkmList, setUmkmList] = useState<UMKM[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingUMKM, setEditingUMKM] = useState<UMKM | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    category: "Kuliner",
    owner_name: "",
    contact_phone: "",
    contact_email: "",
    address: "",
    image_url: "",
  });

  const categories = [
    "Kuliner",
    "Kerajinan",
    "Pertanian",
    "Perdagangan",
    "Jasa",
    "Lainnya",
  ];

  useEffect(() => {
    fetchUMKM();
  }, []);

  const fetchUMKM = async () => {
    try {
      const { data } = await supabase
        .from("umkm")
        .select("*")
        .order("created_at", { ascending: false });

      setUmkmList(data || []);
    } catch (error) {
      console.error("Error fetching UMKM:", error);
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      category: "Kuliner",
      owner_name: "",
      contact_phone: "",
      contact_email: "",
      address: "",
      image_url: "",
    });
    setEditingUMKM(null);
  };

  const openDialog = (umkm?: UMKM) => {
    if (umkm) {
      setEditingUMKM(umkm);
      setFormData({
        name: umkm.name,
        description: umkm.description,
        category: umkm.category,
        owner_name: umkm.owner_name,
        contact_phone: umkm.contact_phone,
        contact_email: umkm.contact_email || "",
        address: umkm.address,
        image_url: umkm.image_url || "",
      });
    } else {
      resetForm();
    }
    setDialogOpen(true);
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      const dataToSave = {
        ...formData,
        contact_email: formData.contact_email || null,
        image_url: formData.image_url || null,
      };

      if (editingUMKM) {
        // Update
        const { error } = await supabase
          .from("umkm")
          .update({
            ...dataToSave,
            updated_at: new Date().toISOString(),
          })
          .eq("id", editingUMKM.id);

        if (error) throw error;
      } else {
        // Create
        const { error } = await supabase.from("umkm").insert([dataToSave]);

        if (error) throw error;
      }

      await fetchUMKM();
      setDialogOpen(false);
      resetForm();
      alert("Data UMKM berhasil disimpan!");
    } catch (error) {
      console.error("Error saving UMKM:", error);
      alert("Gagal menyimpan data UMKM");
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm("Yakin ingin menghapus data UMKM ini?")) return;

    try {
      const { error } = await supabase.from("umkm").delete().eq("id", id);

      if (error) throw error;

      await fetchUMKM();
      alert("Data UMKM berhasil dihapus!");
    } catch (error) {
      console.error("Error deleting UMKM:", error);
      alert("Gagal menghapus data UMKM");
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Kelola UMKM</h2>
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => openDialog()}>
              <Plus className="w-4 h-4 mr-2" />
              Tambah UMKM
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-screen overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {editingUMKM ? "Edit UMKM" : "Tambah UMKM"}
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Nama UMKM</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) =>
                      setFormData({ ...formData, name: e.target.value })
                    }
                    placeholder="Nama usaha"
                  />
                </div>
                <div>
                  <Label htmlFor="category">Kategori</Label>
                  <select
                    id="category"
                    value={formData.category}
                    onChange={(e) =>
                      setFormData({ ...formData, category: e.target.value })
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  >
                    {categories.map((cat) => (
                      <option key={cat} value={cat}>
                        {cat}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              <div>
                <Label htmlFor="description">Deskripsi</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) =>
                    setFormData({ ...formData, description: e.target.value })
                  }
                  placeholder="Deskripsi usaha..."
                  rows={3}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="owner_name">Nama Pemilik</Label>
                  <Input
                    id="owner_name"
                    value={formData.owner_name}
                    onChange={(e) =>
                      setFormData({ ...formData, owner_name: e.target.value })
                    }
                    placeholder="Nama pemilik"
                  />
                </div>
                <div>
                  <Label htmlFor="contact_phone">Telepon</Label>
                  <Input
                    id="contact_phone"
                    value={formData.contact_phone}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        contact_phone: e.target.value,
                      })
                    }
                    placeholder="Nomor telepon"
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="contact_email">Email (Opsional)</Label>
                <Input
                  id="contact_email"
                  type="email"
                  value={formData.contact_email}
                  onChange={(e) =>
                    setFormData({ ...formData, contact_email: e.target.value })
                  }
                  placeholder="Email"
                />
              </div>
              <div>
                <Label htmlFor="address">Alamat</Label>
                <Textarea
                  id="address"
                  value={formData.address}
                  onChange={(e) =>
                    setFormData({ ...formData, address: e.target.value })
                  }
                  placeholder="Alamat lengkap"
                  rows={2}
                />
              </div>
              <div>
                <Label htmlFor="image_url">URL Gambar (Opsional)</Label>
                <Input
                  id="image_url"
                  value={formData.image_url}
                  onChange={(e) =>
                    setFormData({ ...formData, image_url: e.target.value })
                  }
                  placeholder="https://example.com/image.jpg"
                />
              </div>
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setDialogOpen(false)}>
                  Batal
                </Button>
                <Button onClick={handleSave} disabled={saving}>
                  {saving ? "Menyimpan..." : "Simpan"}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid gap-6">
        {umkmList.length > 0 ? (
          umkmList.map((umkm) => (
            <Card key={umkm.id}>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="space-y-2">
                    <CardTitle className="flex items-center space-x-2">
                      <Building2 className="w-5 h-5 text-green-600" />
                      <span>{umkm.name}</span>
                      <span className="bg-blue-500 text-white px-2 py-1 rounded text-xs">
                        {umkm.category}
                      </span>
                    </CardTitle>
                    <div className="text-sm text-gray-600 space-y-1">
                      <p>Pemilik: {umkm.owner_name}</p>
                      <div className="flex items-center space-x-2">
                        <span>📞 {umkm.contact_phone}</span>
                        <a
                          href={`https://wa.me/${
                            umkm.contact_phone.startsWith("08")
                              ? "628" + umkm.contact_phone.slice(2)
                              : umkm.contact_phone
                          }`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-green-600 hover:text-green-800 transition-colors"
                        >
                          <MessageCircle className="w-3 h-3 inline mr-1" />
                          WhatsApp
                        </a>
                      </div>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openDialog(umkm)}
                    >
                      <Edit2 className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(umkm.id)}
                    >
                      <Trash2 className="w-4 h-4 text-red-600" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 mb-2">{umkm.description}</p>
                <p className="text-sm text-gray-600">{umkm.address}</p>
              </CardContent>
            </Card>
          ))
        ) : (
          <Card>
            <CardContent className="text-center py-12">
              <Building2 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 text-lg">Belum ada data UMKM</p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
