"use client";

import { useEffect, useState } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { supabase } from "@/lib/supabase";
import { User, Phone, Mail, Clock, MapPin } from "lucide-react";

interface Contact {
  id: string;
  name: string;
  position: string;
  phone: string;
  email: string | null;
  office_hours: string;
}

export default function KontakPage() {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchContacts();
  }, []);

  const fetchContacts = async () => {
    try {
      const { data } = await supabase
        .from("contacts")
        .select("*")
        .order("created_at", { ascending: true });

      setContacts(data || []);
    } catch (error) {
      console.error("Error fetching contacts:", error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Memuat informasi kontak...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50">
      {/* Hero Section */}
      <section className="bg-green-800 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            Kontak Perangkat Pakan Rabaa Utara Duo
          </h1>
          <p className="text-xl md:text-2xl max-w-3xl mx-auto">
            Hubungi perangkat Pakan Rabaa Utara Duo untuk berbagai keperluan
            layanan masyarakat
          </p>
        </div>
      </section>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Office Information */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center space-x-3">
              <MapPin className="w-6 h-6 text-green-600" />
              <span>Kantor Pakan Rabaa Utara Duo</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-lg mb-2">Alamat</h3>
                <p className="text-gray-700">
                  Jl. Raya Pakan Rabaa Utara Duo No. 1<br />
                  Kecamatan Contoh
                  <br />
                  Kabupaten Contoh
                  <br />
                  Provinsi Contoh
                  <br />
                  16610
                </p>
              </div>
              <div>
                <h3 className="font-semibold text-lg mb-2">Jam Operasional</h3>
                <p className="text-gray-700">
                  Senin - Jumat: 08:00 - 16:00 WIB
                  <br />
                  Sabtu: 08:00 - 12:00 WIB
                  <br />
                  Minggu: Tutup
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Contacts List */}
        {contacts.length > 0 ? (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {contacts.map((contact) => (
              <Card
                key={contact.id}
                className="hover:shadow-lg transition-shadow"
              >
                <CardHeader>
                  <CardTitle className="flex items-center space-x-3">
                    <User className="w-6 h-6 text-blue-600" />
                    <div>
                      <div className="text-xl">{contact.name}</div>
                      <div className="text-sm font-normal text-gray-600">
                        {contact.position}
                      </div>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <Phone className="w-4 h-4 text-green-600" />
                    <a
                      href={`tel:${contact.phone}`}
                      className="text-gray-700 hover:text-green-600 transition-colors"
                    >
                      {contact.phone}
                    </a>
                  </div>

                  {contact.email && (
                    <div className="flex items-center space-x-3">
                      <Mail className="w-4 h-4 text-blue-600" />
                      <a
                        href={`mailto:${contact.email}`}
                        className="text-gray-700 hover:text-blue-600 transition-colors"
                      >
                        {contact.email}
                      </a>
                    </div>
                  )}

                  <div className="flex items-center space-x-3">
                    <Clock className="w-4 h-4 text-purple-600" />
                    <span className="text-gray-700">
                      {contact.office_hours}
                    </span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <Card>
            <CardContent className="text-center py-12">
              <User className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 text-lg">
                Informasi kontak belum tersedia
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
