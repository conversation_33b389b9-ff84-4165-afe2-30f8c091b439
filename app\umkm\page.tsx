"use client";

import { useEffect, useState } from "react";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { supabase } from "@/lib/supabase";
import { Building2, User, MapPin, Phone, Mail } from "lucide-react";

interface UMKM {
  id: string;
  name: string;
  description: string;
  category: string;
  owner_name: string;
  contact_phone: string;
  contact_email: string | null;
  address: string;
  image_url: string | null;
}

export default function UMKMPage() {
  const [umkmList, setUmkmList] = useState<UMKM[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<string>("Semua");

  useEffect(() => {
    fetchUMKM();
  }, []);

  const fetchUMKM = async () => {
    try {
      const { data } = await supabase
        .from("umkm")
        .select("*")
        .order("created_at", { ascending: false });

      setUmkmList(data || []);
    } catch (error) {
      console.error("Error fetching UMKM:", error);
    } finally {
      setLoading(false);
    }
  };

  const categories = [
    "Semua",
    ...Array.from(new Set(umkmList.map((umkm) => umkm.category))),
  ];

  const filteredUMKM =
    selectedCategory === "Semua"
      ? umkmList
      : umkmList.filter((umkm) => umkm.category === selectedCategory);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Memuat data UMKM...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50">
      {/* Hero Section */}
      <section className="bg-green-800 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            Direktori UMKM Pakan Rabaa Utara Duo
          </h1>
          <p className="text-xl md:text-2xl max-w-3xl mx-auto">
            Temukan dan dukung usaha mikro, kecil, dan menengah di Pakan Rabaa
            Utara Duo kami
          </p>
        </div>
      </section>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Category Filter */}
        <div className="flex flex-wrap gap-3 mb-8">
          {categories.map((category) => (
            <Button
              key={category}
              variant={selectedCategory === category ? "default" : "outline"}
              onClick={() => setSelectedCategory(category)}
              className={
                selectedCategory === category
                  ? "bg-green-600 hover:bg-green-700"
                  : ""
              }
            >
              {category}
            </Button>
          ))}
        </div>

        {/* UMKM List */}
        {filteredUMKM.length > 0 ? (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {filteredUMKM.map((umkm) => (
              <Card key={umkm.id} className="hover:shadow-lg transition-shadow">
                {umkm.image_url && (
                  <div className="h-48 bg-gray-200 rounded-t-lg overflow-hidden">
                    <img
                      src={umkm.image_url}
                      alt={umkm.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                )}
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <CardTitle className="text-xl">{umkm.name}</CardTitle>
                    <Badge variant="secondary">{umkm.category}</Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-gray-700">{umkm.description}</p>

                  <div className="space-y-2">
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <User className="w-4 h-4" />
                      <span>{umkm.owner_name}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <MapPin className="w-4 h-4" />
                      <span>{umkm.address}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <Phone className="w-4 h-4" />
                      <a
                        href={`tel:${umkm.contact_phone}`}
                        className="hover:text-green-600"
                      >
                        {umkm.contact_phone}
                      </a>
                    </div>
                    {umkm.contact_email && (
                      <div className="flex items-center space-x-2 text-sm text-gray-600">
                        <Mail className="w-4 h-4" />
                        <a
                          href={`mailto:${umkm.contact_email}`}
                          className="hover:text-green-600"
                        >
                          {umkm.contact_email}
                        </a>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <Card>
            <CardContent className="text-center py-12">
              <Building2 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 text-lg">
                {selectedCategory === "Semua"
                  ? "Belum ada UMKM yang terdaftar"
                  : `Tidak ada UMKM dalam kategori "${selectedCategory}"`}
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
