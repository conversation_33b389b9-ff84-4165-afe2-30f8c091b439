/*
  # Enhanced UMKM Detail Feature

  1. Add new columns to umkm table for detailed information
  2. Create umkm_products table for product/service listings
  3. Create umkm_gallery table for multiple images
  4. Add policies for the new tables

  This migration enhances the UMKM feature with detailed information
  suitable for a comprehensive detail page.
*/

-- Add new columns to umkm table for detailed information
ALTER TABLE umkm ADD COLUMN IF NOT EXISTS 
  business_hours text DEFAULT '08:00 - 17:00',
  established_year integer,
  employee_count integer DEFAULT 1,
  website_url text,
  social_media jsonb DEFAULT '{}',
  business_license text,
  detailed_description text,
  specialties text[],
  price_range text DEFAULT 'Terjangkau',
  delivery_available boolean DEFAULT false,
  online_order_available boolean DEFAULT false,
  payment_methods text[] DEFAULT ARRAY['Tunai'],
  latitude decimal(10,8),
  longitude decimal(11,8),
  status text DEFAULT 'Aktif' CHECK (status IN ('Aktif', 'Tutup Sementara', 'Tutup Permanen'));

-- Create umkm_products table for product/service listings
CREATE TABLE IF NOT EXISTS umkm_products (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  umkm_id uuid NOT NULL REFERENCES umkm(id) ON DELETE CASCADE,
  name text NOT NULL,
  description text,
  price decimal(12,2),
  price_unit text DEFAULT 'per item',
  category text NOT NULL DEFAULT 'Produk',
  image_url text,
  is_available boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create umkm_gallery table for multiple images
CREATE TABLE IF NOT EXISTS umkm_gallery (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  umkm_id uuid NOT NULL REFERENCES umkm(id) ON DELETE CASCADE,
  image_url text NOT NULL,
  caption text,
  is_primary boolean DEFAULT false,
  display_order integer DEFAULT 0,
  created_at timestamptz DEFAULT now()
);

-- Enable RLS on new tables
ALTER TABLE umkm_products ENABLE ROW LEVEL SECURITY;
ALTER TABLE umkm_gallery ENABLE ROW LEVEL SECURITY;

-- Create policies for public read access on new tables
CREATE POLICY "Allow public read access on umkm_products"
  ON umkm_products FOR SELECT
  TO anon, authenticated
  USING (true);

CREATE POLICY "Allow public read access on umkm_gallery"
  ON umkm_gallery FOR SELECT
  TO anon, authenticated
  USING (true);

-- Create policies for authenticated admin users to manage new tables
CREATE POLICY "Allow authenticated users to insert umkm_products"
  ON umkm_products FOR INSERT
  TO authenticated
  WITH CHECK (true);

CREATE POLICY "Allow authenticated users to update umkm_products"
  ON umkm_products FOR UPDATE
  TO authenticated
  USING (true);

CREATE POLICY "Allow authenticated users to delete umkm_products"
  ON umkm_products FOR DELETE
  TO authenticated
  USING (true);

CREATE POLICY "Allow authenticated users to insert umkm_gallery"
  ON umkm_gallery FOR INSERT
  TO authenticated
  WITH CHECK (true);

CREATE POLICY "Allow authenticated users to update umkm_gallery"
  ON umkm_gallery FOR UPDATE
  TO authenticated
  USING (true);

CREATE POLICY "Allow authenticated users to delete umkm_gallery"
  ON umkm_gallery FOR DELETE
  TO authenticated
  USING (true);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_umkm_products_umkm_id ON umkm_products(umkm_id);
CREATE INDEX IF NOT EXISTS idx_umkm_gallery_umkm_id ON umkm_gallery(umkm_id);
CREATE INDEX IF NOT EXISTS idx_umkm_gallery_primary ON umkm_gallery(umkm_id, is_primary);
CREATE INDEX IF NOT EXISTS idx_umkm_status ON umkm(status);
CREATE INDEX IF NOT EXISTS idx_umkm_category ON umkm(category);

-- Insert sample enhanced data for existing UMKM
UPDATE umkm SET 
  business_hours = '08:00 - 21:00',
  established_year = 2018,
  employee_count = 3,
  detailed_description = 'Warung makan keluarga yang menyajikan masakan rumahan dengan cita rasa autentik. Menggunakan bahan-bahan segar dan bumbu tradisional yang diracik secara turun temurun.',
  specialties = ARRAY['Nasi Gudeg', 'Ayam Bakar', 'Soto Ayam', 'Es Teh Manis'],
  price_range = 'Terjangkau',
  delivery_available = true,
  payment_methods = ARRAY['Tunai', 'Transfer Bank', 'E-Wallet'],
  social_media = '{"whatsapp": "***********", "instagram": "@warungbusari"}'::jsonb
WHERE name = 'Warung Bu Sari';

UPDATE umkm SET 
  business_hours = '07:00 - 17:00',
  established_year = 2015,
  employee_count = 5,
  detailed_description = 'Usaha kerajinan bambu yang memproduksi berbagai produk berkualitas tinggi. Menggunakan teknik tradisional yang dipadukan dengan desain modern untuk menghasilkan produk yang unik dan tahan lama.',
  specialties = ARRAY['Keranjang Bambu', 'Furniture Bambu', 'Dekorasi Rumah', 'Souvenir'],
  price_range = 'Menengah',
  online_order_available = true,
  payment_methods = ARRAY['Tunai', 'Transfer Bank'],
  social_media = '{"whatsapp": "***********", "facebook": "Kerajinan Bambu Pak Joko"}'::jsonb
WHERE name = 'Kerajinan Bambu Pak Joko';

-- Insert sample products
INSERT INTO umkm_products (umkm_id, name, description, price, price_unit, category) 
SELECT 
  u.id,
  'Nasi Gudeg Komplit',
  'Nasi gudeg dengan ayam, telur, dan sambal krecek',
  15000,
  'per porsi',
  'Makanan Utama'
FROM umkm u WHERE u.name = 'Warung Bu Sari'
ON CONFLICT DO NOTHING;

INSERT INTO umkm_products (umkm_id, name, description, price, price_unit, category) 
SELECT 
  u.id,
  'Keranjang Bambu Sedang',
  'Keranjang bambu ukuran sedang untuk keperluan rumah tangga',
  75000,
  'per buah',
  'Kerajinan'
FROM umkm u WHERE u.name = 'Kerajinan Bambu Pak Joko'
ON CONFLICT DO NOTHING;

-- Insert sample gallery images
INSERT INTO umkm_gallery (umkm_id, image_url, caption, is_primary, display_order)
SELECT 
  u.id,
  'https://images.unsplash.com/photo-*************-b28f40a0ca4b?w=800',
  'Suasana warung yang nyaman',
  true,
  1
FROM umkm u WHERE u.name = 'Warung Bu Sari'
ON CONFLICT DO NOTHING;

INSERT INTO umkm_gallery (umkm_id, image_url, caption, is_primary, display_order)
SELECT 
  u.id,
  'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800',
  'Produk kerajinan bambu berkualitas',
  true,
  1
FROM umkm u WHERE u.name = 'Kerajinan Bambu Pak Joko'
ON CONFLICT DO NOTHING;
